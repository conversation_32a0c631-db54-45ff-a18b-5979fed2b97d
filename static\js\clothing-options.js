/**
 * 衣物类型和价格选项
 * 用于填充衣物选择下拉菜单
 */

// 全局衣物选项（已移除基础价格，只保留分类和名称）
window.clothingOptions = [
    {
        category: '上衣',
        items: [
            { name: 'T恤' },
            { name: '衬衫' },
            { name: '羊毛衫' },
            { name: '羊绒衫' },
            { name: '西装上衣' },
            { name: '风衣' },
            { name: '羽绒服' },
            { name: '皮衣' },
            { name: '运动服' }
        ]
    },
    {
        category: '裤子',
        items: [
            { name: '西装裤' },
            { name: '牛仔裤' },
            { name: '休闲裤' },
            { name: '运动裤' },
            { name: '短裤' }
        ]
    },
    {
        category: '裙子',
        items: [
            { name: '连衣裙' },
            { name: '短裙' },
            { name: '长裙' },
            { name: '百褶裙' }
        ]
    },
    {
        category: '外套',
        items: [
            { name: '大衣' },
            { name: '棉服' },
            { name: '夹克' }
        ]
    },
    {
        category: '家居服',
        items: [
            { name: '睡衣' },
            { name: '内衣' },
            { name: '家居服' }
        ]
    },
    {
        category: '其他',
        items: [
            { name: '围巾' },
            { name: '帽子' },
            { name: '手套' },
            { name: '领带' },
            { name: '其他' }
        ]
    }
];

// 默认服务价格（作为后备）
let defaultServicePrices = {
    '洗衣': 15,  // 添加洗衣默认价格
    '织补': 20,
    '改衣': 30,
    '其他': 20
};

// 从数据库加载的服务价格
let databaseServicePrices = null;

/**
 * 从服务器获取最新的服务价格配置
 * @returns {Promise} 返回价格配置的Promise
 */
async function loadServicePricesFromDatabase() {
    try {
        const response = await fetch('/api/service_prices');
        if (response.ok) {
            const data = await response.json();
            if (data.success && data.service_prices) {
                databaseServicePrices = data.service_prices;
                // 更新默认价格为数据库中的价格
                defaultServicePrices = { ...data.service_prices };
                console.log('服务价格已从数据库加载:', defaultServicePrices);
                return data.service_prices;
            }
        }
    } catch (error) {
        console.warn('无法从数据库加载服务价格，使用默认价格:', error);
    }
    return defaultServicePrices;
}

/**
 * 获取服务价格（优先使用数据库价格）
 * @param {string} serviceType 服务类型
 * @returns {number} 价格
 */
function getServicePrice(serviceType) {
    const prices = databaseServicePrices || defaultServicePrices;
    return prices[serviceType] || defaultServicePrices[serviceType] || 0;
}

/**
 * 根据衣物名称获取价格（已废弃基础价格概念）
 * @param {string} name 衣物名称
 * @returns {number} 价格（始终返回0，因为已移除基础价格）
 * @deprecated 基础价格概念已移除，请使用服务类型价格
 */
function getClothingPrice(name) {
    // 基础价格概念已移除，始终返回0
    // 应该使用 getServicePrice() 获取具体服务类型的价格
    console.warn('getClothingPrice() 已废弃，基础价格概念已移除，请使用 getServicePrice() 获取服务类型价格');
    return 0;
}

/**
 * 根据衣物名称获取分类
 * @param {string} name 衣物名称
 * @returns {string} 分类名称
 */
function getClothingCategory(name) {
    for (const category of window.clothingOptions) {
        for (const item of category.items) {
            if (item.name === name) {
                return category.category;
            }
        }
    }
    return '其他';
}