/**
 * 移动端专用JavaScript函数
 * 针对移动设备优化的交互逻辑
 */

// 使用已在clothing-options.js中定义的defaultServicePrices

// 全局变量
let currentPhotoItem = null;
let cameraStream = null;
let clothingItemsData = [];
let isMallCustomer = false;
let customerId = null;
let customerDetails = {
    name: '',
    phone: '',
    balance: 0
};

/**
 * 更新衣物项目的总价格
 * @param {Element} itemElement 衣物项目元素
 */
function updateItemPrice(itemElement) {
    const serviceCheckboxes = itemElement.querySelectorAll('.service-type:checked');
    const washPriceInput = itemElement.querySelector('.wash-price');
    const darnPriceInput = itemElement.querySelector('.darn-price');
    const alterPriceInput = itemElement.querySelector('.alter-price');
    const otherPriceInput = itemElement.querySelector('.other-price');
    const totalPriceDisplay = itemElement.querySelector('.clothing-price');
    const quantityInput = itemElement.querySelector('.clothing-quantity');

    let unitPrice = 0;

    serviceCheckboxes.forEach(checkbox => {
        const serviceType = checkbox.value;
        if (serviceType === '洗衣' && washPriceInput) {
            unitPrice += parseFloat(washPriceInput.value || 0);
        } else if (serviceType === '织补' && darnPriceInput) {
            unitPrice += parseFloat(darnPriceInput.value || 0);
        } else if (serviceType === '改衣' && alterPriceInput) {
            unitPrice += parseFloat(alterPriceInput.value || 0);
        } else if (serviceType === '其他' && otherPriceInput) {
            unitPrice += parseFloat(otherPriceInput.value || 0);
        }
    });

    // 获取数量，默认为1
    const quantity = parseInt(quantityInput?.value || 1);

    // 计算总价 = 单价 × 数量
    const totalPrice = unitPrice * quantity;

    if (totalPriceDisplay) {
        totalPriceDisplay.value = totalPrice.toFixed(2);
    }

    console.log(`更新衣物价格: 单价¥${unitPrice.toFixed(2)} × ${quantity}件 = 总价¥${totalPrice.toFixed(2)}`);
}

/**
 * 智能价格分配函数 - 移动端版本
 * @param {number} totalPrice 总价格
 * @param {Element} itemElement 衣物项目元素
 */
function distributePriceToServices(totalPrice, itemElement) {
    const serviceCheckboxes = itemElement.querySelectorAll('.service-type');
    const washPriceInput = itemElement.querySelector('.wash-price');
    const darnPriceInput = itemElement.querySelector('.darn-price');
    const alterPriceInput = itemElement.querySelector('.alter-price');
    const otherPriceInput = itemElement.querySelector('.other-price');

    // 获取当前选中的服务
    const selectedServices = [];
    serviceCheckboxes.forEach(checkbox => {
        if (checkbox.checked) {
            selectedServices.push(checkbox.value);
        }
    });

    if (selectedServices.length === 0) {
        // 如果没有选中任何服务，不进行分配
        return;
    }

    if (selectedServices.length === 1) {
        // 如果只选择了一种服务，将总价全部分配给该服务
        const serviceType = selectedServices[0];
        if (serviceType === '洗衣' && washPriceInput) {
            washPriceInput.value = totalPrice.toFixed(2);
        } else if (serviceType === '织补' && darnPriceInput) {
            darnPriceInput.value = totalPrice.toFixed(2);
        } else if (serviceType === '改衣' && alterPriceInput) {
            alterPriceInput.value = totalPrice.toFixed(2);
        } else if (serviceType === '其他' && otherPriceInput) {
            otherPriceInput.value = totalPrice.toFixed(2);
        }
    } else {
        // 如果选择了多种服务，使用智能分配策略
        let remainingPrice = totalPrice;

        // 优先保持织补、改衣和其他的价格不变，将差额分配给洗衣
        if (selectedServices.includes('织补') && darnPriceInput) {
            const darnPrice = parseFloat(darnPriceInput.value) || defaultServicePrices['织补'];
            remainingPrice -= darnPrice;
        }

        if (selectedServices.includes('改衣') && alterPriceInput) {
            const alterPrice = parseFloat(alterPriceInput.value) || defaultServicePrices['改衣'];
            remainingPrice -= alterPrice;
        }

        if (selectedServices.includes('其他') && otherPriceInput) {
            const otherPrice = parseFloat(otherPriceInput.value) || defaultServicePrices['其他'];
            remainingPrice -= otherPrice;
        }

        // 将剩余价格分配给洗衣服务
        if (selectedServices.includes('洗衣') && washPriceInput) {
            const washPrice = Math.max(0, remainingPrice); // 确保不为负数
            washPriceInput.value = washPrice.toFixed(2);
        }

        // 如果没有洗衣服务，则平均分配给其他服务
        if (!selectedServices.includes('洗衣')) {
            const pricePerService = totalPrice / selectedServices.length;

            if (selectedServices.includes('织补') && darnPriceInput) {
                darnPriceInput.value = pricePerService.toFixed(2);
            }

            if (selectedServices.includes('改衣') && alterPriceInput) {
                alterPriceInput.value = pricePerService.toFixed(2);
            }

            if (selectedServices.includes('其他') && otherPriceInput) {
                otherPriceInput.value = pricePerService.toFixed(2);
            }
        }
    }

    console.log(`移动端价格分配完成 - 总价: ¥${totalPrice}, 选中服务: ${selectedServices.join(', ')}`);
}

/**
 * 页面初始化
 */
document.addEventListener('DOMContentLoaded', async function() {
    console.log("移动端页面加载完成，初始化应用...");

    // 从数据库加载服务价格
    try {
        if (typeof loadServicePricesFromDatabase === 'function') {
            await loadServicePricesFromDatabase();
            console.log("移动端服务价格加载完成:", defaultServicePrices);
        }
    } catch (error) {
        console.warn("移动端服务价格加载失败，使用默认价格:", error);
    }

    try {
        // 确保加载指示器被隐藏
        const processingIndicator = document.getElementById('processingIndicator');
        if (processingIndicator) {
            processingIndicator.style.display = 'none';
            console.log("成功隐藏加载指示器");
        } else {
            console.warn("未找到加载指示器元素");
        }

        // 初始化标签页切换
        initializeTabs();

        // 初始化底部菜单
        initializeBottomMenu();

        // 初始化悬浮按钮
        initializeFloatingButton();

        // 初始化客户类型选择器
        initializeCustomerTypeSelector();

        // 初始化商场客户选择器
        initializeMallCustomerSelector();

        // 初始化充值功能
        initializeRechargeEvents();

        // 初始化摄像头模态框
        initializeCameraModal();

        // 初始化第一个衣物项
        addNewClothingItem();

        // 初始化按钮事件
        initializeButtonEvents();

        // 初始化表单事件
        initializeFormEvents();

        // 初始化打印功能
        initializePrintFunctions();

        // 修复iOS中的一些问题
        fixIOSIssues();

        console.log("移动端页面初始化完成");
    } catch (error) {
        console.error("初始化过程中发生错误:", error);
    }
});

/**
 * 初始化标签页切换
 */
function initializeTabs() {
    // 保存当前标签索引
    let currentTabIndex = 0;
    const tabs = ['customer-tab', 'clothing-tab', 'payment-tab'];

    // 进度条处理函数
    function updateProgressBar(index) {
        const progressBar = document.querySelector('.progress');
        if (progressBar) {
            const percentage = ((index + 1) / tabs.length) * 100;
            progressBar.style.width = percentage + '%';
        }
    }

    document.querySelectorAll('.tab-button').forEach((button, index) => {
        button.addEventListener('click', function() {
            // 移除所有active类
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // 为当前按钮和内容添加active类
            this.classList.add('active');
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');

            // 更新当前标签索引
            currentTabIndex = index;

            // 更新进度条
            updateProgressBar(index);

            // 根据当前标签页显示或隐藏悬浮按钮
            const floatingButton = document.getElementById('floatingAddButton');
            if (tabId === 'clothing-tab') {
                floatingButton.style.display = 'flex';
            } else {
                floatingButton.style.display = 'none';
            }
        });
    });

    // 添加滑动手势
    let startX;
    const tabContents = document.querySelectorAll('.tab-content');

    tabContents.forEach(content => {
        content.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
        }, { passive: true });

        content.addEventListener('touchend', (e) => {
            const endX = e.changedTouches[0].clientX;
            const diffX = endX - startX;

            // 如果滑动距离足够大，进行标签切换
            if (Math.abs(diffX) > 100) {
                let newIndex = currentTabIndex;

                if (diffX > 0 && currentTabIndex > 0) {
                    // 右滑，切换到上一个标签
                    newIndex = currentTabIndex - 1;
                } else if (diffX < 0 && currentTabIndex < tabs.length - 1) {
                    // 左滑，切换到下一个标签
                    newIndex = currentTabIndex + 1;
                }

                if (newIndex !== currentTabIndex) {
                    document.querySelectorAll('.tab-button')[newIndex].click();
                }
            }
        }, { passive: true });
    });
}

/**
 * 初始化底部菜单
 */
function initializeBottomMenu() {
    // 更多菜单点击事件
    const menuMoreBtn = document.getElementById('menuMore');
    if (menuMoreBtn) {
        menuMoreBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // 检查是否已存在更多菜单
            let moreMenu = document.querySelector('.more-menu');

            // 如果不存在，创建菜单
            if (!moreMenu) {
                moreMenu = document.createElement('div');
                moreMenu.className = 'more-menu';
                moreMenu.innerHTML = `
                    <a href="/status/to_factory" class="more-menu-item">
                        <span class="more-menu-icon">🧺</span>
                        <span>送洗处理</span>
                    </a>
                    <a href="/status/factory_in" class="more-menu-item">
                        <span class="more-menu-icon">📥</span>
                        <span>入厂处理</span>
                    </a>
                    <a href="/status/factory_out" class="more-menu-item">
                        <span class="more-menu-icon">📤</span>
                        <span>出厂处理</span>
                    </a>
                    <a href="/status/on_shelf" class="more-menu-item">
                        <span class="more-menu-icon">🧥</span>
                        <span>上架处理</span>
                    </a>
                    <a href="/status/self_pickup" class="more-menu-item">
                        <span class="more-menu-icon">👋</span>
                        <span>自取处理</span>
                    </a>
                    <a href="/data_summary" class="more-menu-item">
                        <span class="more-menu-icon">📊</span>
                        <span>数据汇总</span>
                    </a>
                `;
                document.body.appendChild(moreMenu);

                // 点击外部关闭菜单
                document.addEventListener('click', function closeMenu(e) {
                    if (!moreMenu.contains(e.target) && e.target !== menuMoreBtn) {
                        moreMenu.classList.remove('active');
                        setTimeout(() => {
                            if (moreMenu.parentNode) {
                                moreMenu.parentNode.removeChild(moreMenu);
                            }
                        }, 200);
                        document.removeEventListener('click', closeMenu);
                    }
                });
            }

            // 显示菜单
            moreMenu.classList.add('active');
        });
    }
}

/**
 * 初始化悬浮按钮
 */
function initializeFloatingButton() {
    const floatingButton = document.getElementById('floatingAddButton');
    if (floatingButton) {
        floatingButton.addEventListener('click', function() {
            // 调用添加衣物函数
            addNewClothingItem();

            // 滚动到新添加的衣物项
            setTimeout(() => {
                const newItem = document.querySelector('.clothing-item:last-child');
                if (newItem) {
                    newItem.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }, 100);
        });
    }
}

/**
 * 初始化按钮事件
 */
function initializeButtonEvents() {
    // 添加衣物按钮
    const addClothingBtn = document.getElementById('addClothing');
    if (addClothingBtn) {
        addClothingBtn.addEventListener('click', function() {
            // 折叠所有现有的衣物项
            document.querySelectorAll('.clothing-item').forEach(item => {
                if (!item.classList.contains('collapsed')) {
                    item.classList.add('collapsed');
                    const toggleBtn = item.querySelector('.toggle-collapse');
                    if (toggleBtn) {
                        toggleBtn.textContent = '展开';
                    }
                }
            });

            // 添加新的衣物项
            addNewClothingItem();

            // 滚动到新添加的衣物项
            setTimeout(() => {
                const newItem = document.querySelector('.clothing-item:last-child');
                if (newItem) {
                    newItem.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }, 100);
        });
    }

    // 下一步按钮 - 客户信息到衣物信息
    const nextToClothingBtn = document.getElementById('nextToClothing');
    if (nextToClothingBtn) {
        nextToClothingBtn.addEventListener('click', function() {
            // 获取客户类型
            const customerType = document.querySelector('.customer-type-option.active').getAttribute('data-type');

            if (customerType === 'normal') {
                // 普通客户验证
                const phone = document.getElementById('customerPhone').value;
                if (!phone) {
                    showError('请输入手机号');
                    return;
                }

                if (document.getElementById('customerDetails').classList.contains('hidden')) {
                    const name = document.getElementById('customerName').value;
                    if (!name) {
                        showError('请输入客户姓名');
                        return;
                    }
                }
            } else {
                // 商场客户验证
                const mallCustomerInput = document.getElementById('mallCustomerName');
                const mallCustomerId = mallCustomerInput.dataset.mallCustomerId;
                const mallCustomerName = mallCustomerInput.value;

                if (!mallCustomerId || !mallCustomerName) {
                    showError('请选择有效的商场客户');
                    return;
                }

                // 商场客户已经选择，不需要验证手机号
                document.getElementById('summaryName').textContent = mallCustomerName;
            }

            // 激活下一个标签页
            document.querySelectorAll('.tab-button')[1].click();
        });
    }

    // 下一步按钮 - 衣物信息到订单确认
    const nextToPaymentBtn = document.getElementById('nextToPayment');
    if (nextToPaymentBtn) {
        nextToPaymentBtn.addEventListener('click', function() {
            // 验证衣物信息
            const clothingItemsDom = document.querySelectorAll('.clothing-item');
            let isValid = true;
            let totalPrice = 0;

            // 清空摘要
            document.getElementById('clothingSummary').innerHTML = '';

            // 获取客户类型
            const customerType = document.querySelector('.customer-type-option.active').getAttribute('data-type');

            // 填充客户信息摘要
            if (customerType === 'normal') {
                // 普通客户
                if (document.getElementById('customerDetails').classList.contains('hidden')) {
                    document.getElementById('summaryName').textContent = document.getElementById('customerName').value;
                } else {
                    document.getElementById('summaryName').textContent = document.getElementById('customerNameDisplay').textContent;
                }
                document.getElementById('summaryPhone').textContent = document.getElementById('customerPhone').value;

                // 普通客户 - 确保月结选项不可见，余额选项可见
                const paymentMethodSelect = document.getElementById('paymentMethod');
                if (paymentMethodSelect) {
                    Array.from(paymentMethodSelect.options).forEach(option => {
                        if (option.value === '月结') {
                            option.remove(); // 移除月结选项
                        }
                        if (option.value === '余额') {
                            option.style.display = ''; // 显示余额选项
                        }
                    });
                }
            } else {
                // 商场客户
                const mallCustomerName = document.getElementById('mallCustomerName').value;
                const mallCustomerPhone = document.getElementById('mallCustomerPhone').value || '未提供';

                document.getElementById('summaryName').textContent = mallCustomerName;
                document.getElementById('summaryPhone').textContent = mallCustomerPhone;

                // 商场客户 - 添加月结选项并默认选中
                const paymentMethodSelect = document.getElementById('paymentMethod');
                if (paymentMethodSelect) {
                    // 检查是否已存在月结选项
                    let monthlyOption = Array.from(paymentMethodSelect.options).find(opt => opt.value === '月结');

                    if (!monthlyOption) {
                        monthlyOption = document.createElement('option');
                        monthlyOption.value = '月结';
                        monthlyOption.textContent = '月结';
                        paymentMethodSelect.appendChild(monthlyOption);
                    }

                    // 默认选择月结
                    monthlyOption.selected = true;

                    // 隐藏余额支付选项
                    Array.from(paymentMethodSelect.options).forEach(option => {
                        if (option.value === '余额') {
                            option.style.display = 'none';
                        }
                    });

                    // 隐藏余额信息区域
                    const balanceInfo = document.getElementById('balancePaymentInfo');
                    if (balanceInfo) {
                        balanceInfo.style.display = 'none';
                    }
                }
            }

            // 更新衣物总数 - 修复：使用数量总和而不是条目数
            let totalQuantity = 0;
            clothingItemsDom.forEach(item => {
                const quantityInput = item.querySelector('.clothing-quantity');
                const quantity = parseInt(quantityInput?.value || 1);
                totalQuantity += quantity;
            });
            document.getElementById('totalItems').textContent = totalQuantity;

            // 添加衣物摘要和计算总价
            clothingItemsDom.forEach((item, index) => {
                const nameSelect = item.querySelector('.clothing-name');
                const name = nameSelect.value;
                const color = item.querySelector('.clothing-color').value;
                const price = parseFloat(item.querySelector('.clothing-price').value || 0);
                const note = item.querySelector('.clothing-note').value;

                // 获取数量
                const quantityInput = item.querySelector('.clothing-quantity');
                const quantity = parseInt(quantityInput?.value || 1);

                const serviceTypes = [];

                item.querySelectorAll('.service-type:checked').forEach(checkbox => {
                    serviceTypes.push(checkbox.value);
                });

                // 获取特殊要求
                let requirements = '';
                let serviceDescription = [];

                if (serviceTypes.includes('洗衣')) {
                    const washPrice = parseFloat(item.querySelector('.wash-price').value || 0);
                    serviceDescription.push(`洗衣(¥${washPrice})`);
                }

                if (serviceTypes.includes('织补')) {
                    const darnReq = item.querySelector('.darn-requirement').value;
                    const darnPrice = parseFloat(item.querySelector('.darn-price').value || 0);

                    if (darnReq) {
                        requirements += `织补要求: ${darnReq}; `;
                    }

                    serviceDescription.push(`织补(¥${darnPrice})`);
                }
                if (serviceTypes.includes('改衣')) {
                    const alterReq = item.querySelector('.alter-requirement').value;
                    const alterPrice = parseFloat(item.querySelector('.alter-price').value || 0);

                    if (alterReq) {
                        requirements += `改衣要求: ${alterReq}; `;
                    }

                    serviceDescription.push(`改衣(¥${alterPrice})`);
                }
                if (serviceTypes.includes('其他')) {
                    const otherReq = item.querySelector('.other-requirement').value;
                    const otherPrice = parseFloat(item.querySelector('.other-price').value || 0);

                    if (otherReq) {
                        requirements += `其他要求: ${otherReq}; `;
                    }

                    serviceDescription.push(`其他(¥${otherPrice})`);
                }

                if (!name || serviceTypes.length === 0) {
                    isValid = false;
                    return;
                }

                // 照片检查已改为可选项，不再强制要求
                // const photoThumbnails = item.querySelectorAll('.photo-thumbnail');
                // if (photoThumbnails.length === 0) {
                //     showError(`请为第 ${index + 1} 件衣物拍照`);
                //     isValid = false;
                //     return;
                // }

                totalPrice += price;

                // 创建摘要条目
                const summaryItem = document.createElement('div');
                summaryItem.className = 'summary-item';
                summaryItem.style.animation = `fadeIn 0.3s ease ${index * 0.1}s both`;

                // 处理服务要求的显示
                let detailsHTML = `<p>服务: ${serviceDescription.join(', ')}</p>`;

                // 合并相关要求到一行
                let reqInfo = [];
                if (requirements) reqInfo.push(requirements);
                if (note) reqInfo.push(`备注: ${note}`);

                if (reqInfo.length > 0) {
                    detailsHTML += `<p>${reqInfo.join(' | ')}</p>`;
                }

                // 获取衣物的第一张照片作为缩略图
                let thumbnailHTML = '';
                if (photoThumbnails.length > 0) {
                    thumbnailHTML = `
                        <div style="width: 40px; height: 40px; margin-right: 10px; flex-shrink: 0; border-radius: 4px; overflow: hidden;">
                            <img src="${photoThumbnails[0].src}" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                    `;
                }

                summaryItem.innerHTML = `
                    <div class="summary-item-header">
                        <span>${index + 1}. ${name} (${color}) × ${quantity}</span>
                        <span class="summary-item-price">¥${price.toFixed(2)}</span>
                    </div>
                    <div class="summary-item-content">
                        ${detailsHTML}
                    </div>
                `;

                document.getElementById('clothingSummary').appendChild(summaryItem);
            });

            if (!isValid) {
                showError('请完善所有衣物信息');
                return;
            }

            // 更新总价
            document.getElementById('totalAmount').textContent = totalPrice.toFixed(2);

            // 激活下一个标签页
            document.querySelectorAll('.tab-button')[2].click();
        });
    }

    // 提交订单按钮
    const submitOrderBtn = document.getElementById('submitOrder');
    if (submitOrderBtn) {
        submitOrderBtn.addEventListener('click', function() {
            // 验证客户信息
            const customerType = document.querySelector('.customer-type-option.active').getAttribute('data-type');

            if (customerType === 'normal') {
                const customerName = document.getElementById('customerNameDisplay').textContent || document.getElementById('customerName').value;
                const customerPhone = document.getElementById('customerPhone').value;

                if (!customerName || !customerPhone) {
                    showError('请填写完整的客户信息');
                    return;
                }
            } else {
                const mallCustomerName = document.getElementById('mallCustomerName').value;
                const mallCustomerId = document.getElementById('mallCustomerName').dataset.mallCustomerId;

                if (!mallCustomerName || !mallCustomerId) {
                    showError('请选择商场客户');
                    return;
                }
            }

            // 验证衣物信息
            const clothingItems = document.querySelectorAll('.clothing-item');
            if (clothingItems.length === 0) {
                showError('请至少添加一件衣物');
                return;
            }

            // 显示加载指示器
            showLoading('正在提交订单...');

            // 收集所有数据
            const orderData = collectOrderData();
            console.log('提交订单数据:', orderData);

            // 发送订单数据到服务器
            fetch('/submit_order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(orderData)
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(errorData => {
                        throw new Error(errorData.error || `服务器错误 (${response.status})`);
                    });
                }
                return response.json();
            })
            .then(data => {
                // 隐藏加载指示器
                hideLoading();

                if (data.success) {
                    // 显示成功消息
                    const resultsDiv = document.getElementById('orderResults');
                    resultsDiv.innerHTML = `
                        <div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <h3 style="margin-top: 0;">订单提交成功</h3>
                            <p>订单号: <strong>${data.order_number}</strong></p>
                            <p>总金额: <strong>¥${data.total_amount.toFixed(2)}</strong></p>
                        </div>
                        <div style="display: flex; gap: 10px; margin-top: 15px;">
                            <button type="button" id="printReceiptButton" style="flex: 1; background: #007bff;">打印小票</button>
                            <button type="button" id="printLabelsButton" style="flex: 1; background: #28a745;">打印水洗唛</button>
                        </div>
                        <button type="button" id="newOrderButton" style="width: 100%; margin-top: 15px; background: #6c757d;">创建新订单</button>
                    `;

                    // 添加打印按钮事件
                    document.getElementById('printReceiptButton').addEventListener('click', function() {
                        showReceiptModal(data.order_id);
                    });

                    document.getElementById('printLabelsButton').addEventListener('click', function() {
                        showLabelModal(data.order_id);
                    });

                    document.getElementById('newOrderButton').addEventListener('click', function() {
                        window.location.href = '/mobile';
                    });
                } else {
                    // 显示错误消息
                    document.getElementById('orderResults').innerHTML = `
                        <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px;">
                            <h3 style="margin-top: 0;">订单提交失败</h3>
                            <p>${data.error || '服务器错误，请重试'}</p>
                        </div>
                        <button type="button" id="retryButton" style="width: 100%; margin-top: 15px;">重试</button>
                    `;

                    document.getElementById('retryButton').addEventListener('click', function() {
                        document.getElementById('submitOrder').click();
                    });
                }
            })
            .catch(error => {
                hideLoading();
                console.error('提交订单失败:', error);
                document.getElementById('orderResults').innerHTML = `
                    <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px;">
                        <h3 style="margin-top: 0;">订单提交失败</h3>
                        <p>${error.message || '连接服务器失败，请检查网络连接后重试'}</p>
                    </div>
                    <button type="button" id="retryButton" style="width: 100%; margin-top: 15px;">重试</button>
                `;

                document.getElementById('retryButton').addEventListener('click', function() {
                    document.getElementById('submitOrder').click();
                });
            });
        });
    }
}

/**
 * 初始化表单事件
 */
function initializeFormEvents() {
    // 设置手机号输入延时搜索
    let searchTimeout = null;
    const phoneInput = document.getElementById('customerPhone');
    if (phoneInput) {
        phoneInput.addEventListener('input', function() {
            const phone = this.value;

            // 清除之前的定时器
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            // 设置新的定时器，300毫秒后执行搜索（防止频繁请求）
            searchTimeout = setTimeout(() => {
                searchCustomerByPhone(phone);
            }, 300);
        });
    }

    // 查询按钮
    const searchCustomerBtn = document.getElementById('searchCustomer');
    if (searchCustomerBtn) {
        searchCustomerBtn.addEventListener('click', async function() {
            const phone = document.getElementById('customerPhone').value;
            if (!phone) {
                showError('请输入手机号');
                return;
            }

            await searchCustomerByPhone(phone);
        });
    }

    // 直接充值按钮
    const directRechargeBtn = document.getElementById('directRecharge');
    if (directRechargeBtn) {
        directRechargeBtn.addEventListener('click', function() {
            const phone = document.getElementById('customerPhone').value;
            if (!phone) {
                showError('请输入手机号');
                return;
            }

            // 打开充值弹窗
            const modal = document.getElementById('rechargeModal');
            if (modal) {
                modal.style.display = 'block';

                // 查询用户状态
                fetch(`/search_customer?phone=${phone}`)
                    .then(response => response.json())
                    .then(data => {
                        const statusElem = document.getElementById('rechargeUserStatus');
                        if (data.found) {
                            // 显示用户信息
                            statusElem.innerHTML = `为用户 <strong>${data.name}</strong> 充值，当前余额: ¥${data.balance.toFixed(2)}`;
                            statusElem.style.color = '#28a745';

                            // 优化：自动同步姓名到新客户表单（以备后续流程使用）
                            document.getElementById('customerName').value = data.name;

                            // 同步显示用户信息到主界面
                            document.getElementById('customerNameDisplay').textContent = data.name;
                            document.getElementById('customerBalance').textContent = data.balance.toFixed(2);
                            document.getElementById('customerDetails').classList.remove('hidden');
                            document.getElementById('newCustomerForm').classList.add('hidden');

                            // 更新摘要页面的客户名称
                            document.getElementById('summaryName').textContent = data.name;
                        } else {
                            // 显示新用户提示
                            statusElem.innerHTML = `将为新用户 <strong>${phone}</strong> 创建账户并充值`;
                            statusElem.style.color = '#007BFF';

                            // 清空表单，准备填写新用户信息
                            document.getElementById('customerName').value = '';
                            document.getElementById('customerDetails').classList.add('hidden');
                            document.getElementById('newCustomerForm').classList.remove('hidden');
                            document.getElementById('summaryName').textContent = '';
                        }
                    })
                    .catch(error => {
                        document.getElementById('rechargeUserStatus').innerHTML = '查询用户信息失败';
                        document.getElementById('rechargeUserStatus').style.color = '#dc3545';
                    });

                // 重置选择状态
                document.querySelectorAll('.recharge-amount-option').forEach(option => {
                    option.classList.remove('selected');
                });
                document.querySelectorAll('.recharge-payment-method').forEach(method => {
                    method.classList.remove('selected');
                });

                const customAmountInput = document.getElementById('customRechargeAmount');
                if (customAmountInput) {
                    customAmountInput.value = '';
                }
            } else {
                console.error('未找到充值弹窗(#rechargeModal)');
            }
        });
    }

    // 处理余额支付相关逻辑
    const paymentMethodSelect = document.getElementById('paymentMethod');
    if (paymentMethodSelect) {
        paymentMethodSelect.addEventListener('change', function() {
            const balanceInfo = document.getElementById('balancePaymentInfo');
            const balanceStatus = document.getElementById('balanceStatus');
            const totalAmount = parseFloat(document.getElementById('totalAmount').textContent);
            const customerBalance = parseFloat(document.getElementById('customerBalance').textContent);

            if (this.value === '余额') {
                // 显示余额支付信息
                balanceInfo.style.display = 'block';
                document.getElementById('summaryBalance').textContent = customerBalance.toFixed(2);

                // 检查余额是否足够
                if (customerBalance >= totalAmount) {
                    balanceStatus.textContent = '余额充足';
                    balanceStatus.style.color = '#28a745';
                } else {
                    balanceStatus.textContent = '余额不足，请先充值';
                    balanceStatus.style.color = '#dc3545';
                }
            } else {
                // 隐藏余额支付信息
                balanceInfo.style.display = 'none';
            }
        });
    }
}

/**
 * 初始化客户类型选择器
 */
function initializeCustomerTypeSelector() {
    const options = document.querySelectorAll('.customer-type-option');
    const phoneFormGroup = document.querySelector('.form-group:has(#customerPhone)');
    const mallSelector = document.querySelector('.mall-customer-selector');

    options.forEach(option => {
        option.addEventListener('click', function() {
            // 移除所有active类
            options.forEach(opt => opt.classList.remove('active'));

            // 添加当前选项的active类
            this.classList.add('active');

            // 根据选择显示/隐藏表单
            const type = this.getAttribute('data-type');
            if (type === 'normal') {
                // 普通客户设置
                phoneFormGroup.style.display = 'block';
                document.getElementById('customerDetails').style.display = '';
                document.getElementById('newCustomerForm').style.display = '';
                mallSelector.style.display = 'none';
                // 设置为普通客户
                isMallCustomer = false;

                // 普通客户 - 确保月结选项不可见，余额选项可见
                const paymentMethodSelect = document.getElementById('paymentMethod');
                if (paymentMethodSelect) {
                    Array.from(paymentMethodSelect.options).forEach(option => {
                        if (option.value === '月结') {
                            option.remove(); // 移除月结选项
                        }
                        if (option.value === '余额') {
                            option.style.display = ''; // 显示余额选项
                        }
                    });
                }
            } else if (type === 'mall') {
                // 商场客户设置
                phoneFormGroup.style.display = 'none';
                document.getElementById('customerDetails').style.display = 'none';
                document.getElementById('newCustomerForm').style.display = 'none';
                mallSelector.style.display = 'block';
                // 设置为商场客户
                isMallCustomer = true;
                // 重置普通客户ID和详细信息
                customerId = null;
                customerDetails = {
                    name: '',
                    phone: '',
                    balance: 0
                };

                // 商场客户 - 添加月结选项并默认选中
                const paymentMethodSelect = document.getElementById('paymentMethod');
                if (paymentMethodSelect) {
                    // 检查是否已存在月结选项
                    let monthlyOption = Array.from(paymentMethodSelect.options).find(opt => opt.value === '月结');

                    if (!monthlyOption) {
                        monthlyOption = document.createElement('option');
                        monthlyOption.value = '月结';
                        monthlyOption.textContent = '月结';
                        paymentMethodSelect.appendChild(monthlyOption);
                    }

                    // 默认选择月结
                    monthlyOption.selected = true;

                    // 隐藏余额支付选项
                    Array.from(paymentMethodSelect.options).forEach(option => {
                        if (option.value === '余额') {
                            option.style.display = 'none';
                        }
                    });

                    // 隐藏余额信息区域
                    const balanceInfo = document.getElementById('balancePaymentInfo');
                    if (balanceInfo) {
                        balanceInfo.style.display = 'none';
                    }
                }
            }
        });
    });
}

/**
 * 初始化商场客户选择器
 */
function initializeMallCustomerSelector() {
    const searchInput = document.getElementById('mallCustomerName');
    const phoneInput = document.getElementById('mallCustomerPhone');

    if (!searchInput) return; // 防止元素不存在导致错误

    // 创建下拉列表
    const dropdown = document.createElement('div');
    dropdown.classList.add('mall-customer-dropdown');
    dropdown.style.display = 'none';
    dropdown.style.position = 'absolute';
    dropdown.style.width = '100%';
    dropdown.style.maxHeight = '200px';
    dropdown.style.overflowY = 'auto';
    dropdown.style.background = '#fff';
    dropdown.style.border = '1px solid #ddd';
    dropdown.style.borderRadius = '8px';
    dropdown.style.zIndex = '100';
    dropdown.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
    searchInput.parentNode.style.position = 'relative';
    searchInput.parentNode.appendChild(dropdown);

    // 添加输入事件
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.trim();
        if (searchTerm.length < 2) {
            dropdown.style.display = 'none';
            return;
        }

        // 搜索商场客户
        fetch(`/api/mall_customers?search=${searchTerm}&page=1&per_page=5`)
            .then(response => response.json())
            .then(data => {
                dropdown.innerHTML = '';
                if (data.customers && data.customers.length > 0) {
                    data.customers.forEach(customer => {
                        const item = document.createElement('div');
                        item.style.padding = '10px 12px';
                        item.style.cursor = 'pointer';
                        item.style.borderBottom = '1px solid #eee';
                        item.style.transition = 'background 0.2s';
                        item.innerHTML = `
                            <div><strong>${customer.mall_name}</strong></div>
                            <div style="font-size: 0.85rem; color: #666;">
                                联系人: ${customer.contact_name || '-'} | 电话: ${customer.phone || '-'}
                            </div>
                        `;

                        item.addEventListener('touchstart', function() {
                            this.style.background = '#f5f5f5';
                        });

                        item.addEventListener('touchend', function() {
                            this.style.background = '#fff';
                        });

                        item.addEventListener('click', function() {
                            searchInput.value = customer.mall_name;
                            phoneInput.value = customer.phone || '';
                            dropdown.style.display = 'none';

                            // 存储客户ID以便后续使用
                            searchInput.dataset.mallCustomerId = customer.id;

                            // 存储当前商场客户信息
                            window.currentMallCustomer = customer;

                            // 获取该商场客户的所有折扣
                            fetch(`/api/mall_customers/${customer.id}/discounts`)
                                .then(response => response.json())
                                .then(discountData => {
                                    // 存储折扣信息
                                    window.mallCustomerDiscounts = discountData.discounts || [];
                                    console.log('加载商场客户折扣:', window.mallCustomerDiscounts);

                                    // 重新计算所有已添加衣物的折扣
                                    applyDiscountsToExistingItemsMobile();
                                })
                                .catch(error => console.error('获取折扣信息失败:', error));
                        });

                        dropdown.appendChild(item);
                    });
                    dropdown.style.display = 'block';
                } else {
                    dropdown.innerHTML = '<div style="padding: 10px 12px; color: #999;">没有找到匹配的商场品牌</div>';
                    dropdown.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('搜索商场品牌失败:', error);
                dropdown.innerHTML = '<div style="padding: 10px 12px; color: #999;">搜索失败，请重试</div>';
                dropdown.style.display = 'block';
            });
    });

    // 点击外部区域关闭下拉列表
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
            dropdown.style.display = 'none';
        }
    });
}

/**
 * 计算赠送金额选项（移动端）
 */
function calculateGiftAmountMobile(amount) {
    if (!amount || amount <= 0) {
        const giftRulesContainer = document.getElementById('giftRulesContainer');
        if (giftRulesContainer) {
            giftRulesContainer.style.display = 'none';
        }
        document.getElementById('giftAmountDisplay').textContent = '¥0.00';
        return;
    }

    fetch('/api/calculate_gift_options', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ amount: amount })
    })
    .then(response => response.json())
    .then(data => {
        if (data.options && data.options.length > 0) {
            // 显示规则选择区域
            const giftRulesContainer = document.getElementById('giftRulesContainer');
            if (giftRulesContainer) {
                giftRulesContainer.style.display = 'block';

                // 渲染规则选项
                let html = '';
                data.options.forEach((option, index) => {
                    const isRecommended = data.recommended && option.rule_id === data.recommended.rule_id;
                    const checkedAttr = index === 0 ? 'checked' : ''; // 默认选择第一个（最优）
                    const recommendedBadge = isRecommended ? '<span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em; margin-left: 5px;">推荐</span>' : '';

                    html += `
                        <div style="margin: 8px 0; padding: 8px; border: 1px solid #ddd; border-radius: 6px;">
                            <label style="display: flex; align-items: center; cursor: pointer; margin: 0; text-align: left;">
                                <input type="radio" name="giftRule" value="${option.rule_id}"
                                       data-gift-amount="${option.gift_amount}" ${checkedAttr}
                                       style="margin-right: 8px;">
                                <span style="font-size: 0.85em; text-align: left;">${option.description} (赠送¥${option.gift_amount.toFixed(2)})${recommendedBadge}</span>
                            </label>
                        </div>
                    `;
                });

                document.getElementById('giftRulesOptions').innerHTML = html;

                // 绑定选择事件
                document.querySelectorAll('input[name="giftRule"]').forEach(radio => {
                    radio.addEventListener('change', function() {
                        const giftAmount = parseFloat(this.getAttribute('data-gift-amount'));
                        document.getElementById('giftAmountDisplay').textContent = `¥${giftAmount.toFixed(2)}`;
                    });
                });

                // 设置默认赠送金额
                const defaultGiftAmount = data.options[0].gift_amount;
                document.getElementById('giftAmountDisplay').textContent = `¥${defaultGiftAmount.toFixed(2)}`;
            }
        } else {
            const giftRulesContainer = document.getElementById('giftRulesContainer');
            if (giftRulesContainer) {
                giftRulesContainer.style.display = 'none';
            }
            document.getElementById('giftAmountDisplay').textContent = '¥0.00';
        }
    })
    .catch(error => {
        console.error('计算赠送选项失败:', error);
        const giftRulesContainer = document.getElementById('giftRulesContainer');
        if (giftRulesContainer) {
            giftRulesContainer.style.display = 'none';
        }
        document.getElementById('giftAmountDisplay').textContent = '¥0.00';
    });
}

/**
 * 初始化充值功能
 */
function initializeRechargeEvents() {
    // 关闭充值弹窗
    const closeRechargeBtn = document.getElementById('closeRechargeModal');
    if (closeRechargeBtn) {
        closeRechargeBtn.addEventListener('click', function() {
            document.getElementById('rechargeModal').style.display = 'none';
        });
    }

    // 充值金额选项
    const rechargeOptions = document.querySelectorAll('.recharge-amount-option');
    rechargeOptions.forEach(option => {
        option.addEventListener('click', function() {
            // 移除其他选项的选中状态
            rechargeOptions.forEach(opt => opt.classList.remove('selected'));

            // 添加当前选项的选中状态
            this.classList.add('selected');

            // 清空自定义金额
            document.getElementById('customRechargeAmount').value = '';

            // 计算赠送金额
            const amount = parseFloat(this.getAttribute('data-amount'));
            calculateGiftAmountMobile(amount);
        });
    });

    // 自定义金额输入
    const customAmountInput = document.getElementById('customRechargeAmount');
    if (customAmountInput) {
        customAmountInput.addEventListener('input', function() {
            if (this.value) {
                // 清除预设金额的选中状态
                rechargeOptions.forEach(opt => opt.classList.remove('selected'));

                // 计算赠送金额
                const amount = parseFloat(this.value);
                calculateGiftAmountMobile(amount);
            } else {
                // 清空时重置赠送金额显示
                document.getElementById('giftAmountDisplay').textContent = '¥0.00';
            }
        });
    }

    // 支付方式选项
    const paymentMethods = document.querySelectorAll('.recharge-payment-method');
    paymentMethods.forEach(method => {
        method.addEventListener('click', function() {
            // 移除其他选项的选中状态
            paymentMethods.forEach(m => m.classList.remove('selected'));

            // 添加当前选项的选中状态
            this.classList.add('selected');
        });
    });

    // 确认充值按钮
    const confirmRechargeBtn = document.getElementById('confirmRecharge');
    if (confirmRechargeBtn) {
        confirmRechargeBtn.addEventListener('click', function() {
            // 获取充值金额
            let amount = 0;
            const customAmount = document.getElementById('customRechargeAmount').value;

            if (customAmount && !isNaN(customAmount) && parseFloat(customAmount) > 0) {
                amount = parseFloat(customAmount);
            } else {
                const selectedOption = document.querySelector('.recharge-amount-option.selected');
                if (selectedOption) {
                    amount = parseFloat(selectedOption.getAttribute('data-amount'));
                }
            }

            // 获取支付方式
            const selectedMethod = document.querySelector('.recharge-payment-method.selected');
            let paymentMethod = '';

            if (selectedMethod) {
                paymentMethod = selectedMethod.getAttribute('data-method');
            }

            // 验证输入
            if (amount <= 0) {
                showError('请选择或输入有效的充值金额');
                return;
            }

            if (!paymentMethod) {
                showError('请选择支付方式');
                return;
            }

            // 显示加载指示器
            showLoading('正在处理充值...');

            // 获取客户信息
            const phone = document.getElementById('customerPhone').value;
            const name = document.getElementById('customerName').value;

            // 准备充值数据
            const rechargeData = {
                phone: phone,
                name: name,
                amount: amount,
                payment_method: paymentMethod
            };

            // 获取选择的规则
            const selectedRule = document.querySelector('input[name="giftRule"]:checked');
            if (selectedRule) {
                rechargeData.selected_rule_id = parseInt(selectedRule.value);
            }

            // 发送充值请求
            fetch('/recharge', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(rechargeData)
            })
            .then(response => response.json())
            .then(data => {
                // 隐藏加载指示器
                hideLoading();

                if (data.success) {
                    // 更新余额显示
                    document.getElementById('customerBalance').textContent = data.newBalance.toFixed(2);

                    // 关闭充值弹窗
                    document.getElementById('rechargeModal').style.display = 'none';

                    // 构建充值成功消息
                    let successMessage = `充值成功，当前余额: ¥${data.newBalance.toFixed(2)}`;
                    if (data.giftAmount && data.giftAmount > 0) {
                        successMessage += `，赠送金额: ¥${data.giftAmount.toFixed(2)}`;
                    }

                    // 显示成功消息
                    showSuccess(successMessage);

                    // 更新客户详情显示
                    document.getElementById('customerNameDisplay').textContent = data.customer_name;
                    document.getElementById('customerDetails').classList.remove('hidden');
                    document.getElementById('newCustomerForm').classList.add('hidden');

                    // 更新客户ID
                    customerId = data.customer_id;

                    // 更新摘要页面的客户名称
                    document.getElementById('summaryName').textContent = data.customer_name;
                } else {
                    showError(data.error || '充值失败，请重试');
                }
            })
            .catch(error => {
                hideLoading();
                console.error('充值请求失败:', error);
                showError('网络错误，请检查连接后重试');
            });
        });
    }
}

/**
 * 初始化摄像头模态框
 */
function initializeCameraModal() {
    const cameraModal = document.getElementById('cameraModal');
    const closeModalBtn = document.querySelector('.close-modal');
    const takePhotoBtn = document.getElementById('takePhotoBtn');
    const cancelBtn = document.getElementById('cancelBtn');
    const cameraFeed = document.getElementById('modalCameraFeed');
    const photoCanvas = document.getElementById('photoCanvas');

    if (!cameraModal) return;

    // 关闭摄像头模态框
    function closeCamera() {
        cameraModal.style.display = 'none';

        // 停止摄像头流
        if (cameraStream) {
            cameraStream.getTracks().forEach(track => track.stop());
            cameraStream = null;
        }
    }

    // 关闭按钮事件
    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', closeCamera);
    }

    // 取消按钮事件
    if (cancelBtn) {
        cancelBtn.addEventListener('click', closeCamera);
    }

    // 拍照按钮事件
    if (takePhotoBtn) {
        takePhotoBtn.addEventListener('click', function() {
            if (!cameraStream) return;

            // 设置画布尺寸与视频流相同
            const width = cameraFeed.videoWidth;
            const height = cameraFeed.videoHeight;
            photoCanvas.width = width;
            photoCanvas.height = height;

            // 在画布上绘制当前视频帧
            const context = photoCanvas.getContext('2d');
            context.drawImage(cameraFeed, 0, 0, width, height);

            // 获取图像数据
            const imageData = photoCanvas.toDataURL('image/jpeg', 0.8);

            // 使用ImageCompressor库压缩图像
            window.ImageCompressor.compress(imageData, {
                maxWidth: 800,
                maxHeight: 800,
                quality: 0.8
            }).then(compressedImage => {
                console.log("图片压缩成功，准备添加到衣物项");

                // 添加照片到当前衣物项
                if (currentPhotoItem) {
                    const thumbnailsContainer = currentPhotoItem.querySelector('.photo-thumbnails');

                    if (!thumbnailsContainer) {
                        console.error("未找到照片缩略图容器");
                        return;
                    }

                    // 创建缩略图容器
                    const thumbnailContainer = document.createElement('div');
                    thumbnailContainer.className = 'photo-thumbnail-container';

                    // 创建缩略图
                    const thumbnail = document.createElement('img');
                    thumbnail.className = 'photo-thumbnail';
                    thumbnail.src = compressedImage;
                    thumbnail.dataset.fullImage = compressedImage;

                    // 创建删除按钮
                    const removeBtn = document.createElement('div');
                    removeBtn.className = 'remove-photo';
                    removeBtn.innerHTML = '×';
                    removeBtn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        thumbnailContainer.remove();
                    });

                    // 添加到容器
                    thumbnailContainer.appendChild(thumbnail);
                    thumbnailContainer.appendChild(removeBtn);
                    thumbnailsContainer.appendChild(thumbnailContainer);

                    // 点击缩略图查看大图
                    thumbnail.addEventListener('click', function() {
                        showImagePreview(this.dataset.fullImage);
                    });

                    console.log("照片已成功添加到衣物项");
                } else {
                    console.error("未找到当前操作的衣物项");
                }

                // 关闭摄像头
                closeCamera();
            }).catch(error => {
                console.error("图片压缩失败:", error);
                showError("图片处理失败，请重试");
                closeCamera();
            });
        });
    }
}

/**
 * 添加新衣物项
 */
function addNewClothingItem() {
    const clothingItems = document.getElementById('clothingItems');
    const template = document.getElementById('clothing-item-template');

    if (!clothingItems || !template) {
        console.error('未找到衣物项容器或模板');
        return;
    }

    // 克隆模板
    const newItem = document.importNode(template.content, true).firstElementChild;

    // 设置唯一ID
    const itemId = 'clothing-' + Date.now();
    newItem.id = itemId;

    // 添加到容器
    clothingItems.appendChild(newItem);

    // 初始化衣物名称选项
    const nameSelect = newItem.querySelector('.clothing-name');
    if (nameSelect && window.clothingOptions) {
        // 清空现有选项
        nameSelect.innerHTML = '<option value="">请选择衣物类型</option>';

        // 添加衣物类型选项
        window.clothingOptions.forEach(option => {
            const optGroup = document.createElement('optgroup');
            optGroup.label = option.category;

            option.items.forEach(item => {
                const opt = document.createElement('option');
                opt.value = item.name;
                opt.textContent = item.name;
                opt.dataset.price = item.price;
                optGroup.appendChild(opt);
            });

            nameSelect.appendChild(optGroup);
        });

        // 添加选择事件
        nameSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];

            // 更新衣物项标题
            const title = newItem.querySelector('.clothing-item-title');
            if (title && selectedOption.value) {
                title.textContent = selectedOption.value;
            }

            // 基础价格概念已移除，选择商品时使用默认服务价格
            if (selectedOption.value) {
                console.log(`移动端选择商品: ${selectedOption.value}，使用默认服务价格`);

                // 使用数据库中的默认服务价格
                const washPriceInput = newItem.querySelector('.wash-price');
                if (washPriceInput && typeof getServicePrice === 'function') {
                    washPriceInput.value = getServicePrice('洗衣').toFixed(2);
                }
            }

            // 应用商场客户折扣（如果适用）
            if (window.currentMallCustomer) {
                applyDiscountToItemMobile(newItem);
            }

            // 重新计算价格（基于当前选中的服务）
            updateItemPrice(newItem);
        });
    }

    // 初始化服务类型复选框
    const serviceCheckboxes = newItem.querySelectorAll('.service-type');
    serviceCheckboxes.forEach(checkbox => {
        // 设置初始状态
        if (checkbox.checked) {
            const serviceType = checkbox.value;
            if (serviceType === '洗衣') {
                const washRequirements = newItem.querySelector('.wash-requirements');
                if (washRequirements) {
                    washRequirements.style.display = 'block';
                }
            } else if (serviceType === '织补') {
                const darnRequirements = newItem.querySelector('.darn-requirements');
                if (darnRequirements) {
                    darnRequirements.style.display = 'block';
                }
            } else if (serviceType === '改衣') {
                const alterRequirements = newItem.querySelector('.alter-requirements');
                if (alterRequirements) {
                    alterRequirements.style.display = 'block';
                }
            }

            // 添加选中样式到父级label
            const parentLabel = checkbox.closest('label');
            if (parentLabel) {
                parentLabel.classList.add('selected');
            }
        }

        // 添加change事件监听器
        checkbox.addEventListener('change', function() {
            const serviceType = this.value;
            const isChecked = this.checked;

            // 更新父级label的样式
            const parentLabel = this.closest('label');
            if (parentLabel) {
                if (isChecked) {
                    parentLabel.classList.add('selected');
                } else {
                    parentLabel.classList.remove('selected');
                }
            }

            // 根据服务类型显示/隐藏相应的要求输入区域
            if (serviceType === '洗衣') {
                const washRequirements = newItem.querySelector('.wash-requirements');
                if (washRequirements) {
                    washRequirements.style.display = isChecked ? 'block' : 'none';
                }
            } else if (serviceType === '织补') {
                const darnRequirements = newItem.querySelector('.darn-requirements');
                if (darnRequirements) {
                    darnRequirements.style.display = isChecked ? 'block' : 'none';
                }
            } else if (serviceType === '改衣') {
                const alterRequirements = newItem.querySelector('.alter-requirements');
                if (alterRequirements) {
                    alterRequirements.style.display = isChecked ? 'block' : 'none';
                }
            } else if (serviceType === '其他') {
                const otherRequirements = newItem.querySelector('.other-requirements');
                if (otherRequirements) {
                    otherRequirements.style.display = isChecked ? 'block' : 'none';
                }
            }

            // 更新价格
            updateItemPrice(newItem);

            console.log(`服务类型 ${serviceType} 已${isChecked ? '选中' : '取消选中'}`);
        });
    });

    // 添加价格输入框的事件监听器
    const washPriceInput = newItem.querySelector('.wash-price');
    const darnPriceInput = newItem.querySelector('.darn-price');
    const alterPriceInput = newItem.querySelector('.alter-price');
    const otherPriceInput = newItem.querySelector('.other-price');
    const totalPriceInput = newItem.querySelector('.clothing-price');

    if (washPriceInput) {
        washPriceInput.addEventListener('input', () => updateItemPrice(newItem));
    }
    if (darnPriceInput) {
        darnPriceInput.addEventListener('input', () => updateItemPrice(newItem));
    }
    if (alterPriceInput) {
        alterPriceInput.addEventListener('input', () => updateItemPrice(newItem));
    }
    if (otherPriceInput) {
        otherPriceInput.addEventListener('input', () => updateItemPrice(newItem));
    }

    // 总价变更事件 - 智能分配价格到各服务项
    if (totalPriceInput) {
        totalPriceInput.addEventListener('change', function() {
            const newTotalPrice = parseFloat(this.value) || 0;
            distributePriceToServices(newTotalPrice, newItem);
        });
    }

    // 初始化展开/折叠按钮
    const toggleBtn = newItem.querySelector('.toggle-collapse');
    if (toggleBtn) {
        toggleBtn.addEventListener('click', function() {
            newItem.classList.toggle('collapsed');

            // 更新图标方向
            const toggleIcon = this.querySelector('.toggle-icon');
            if (toggleIcon) {
                if (newItem.classList.contains('collapsed')) {
                    toggleIcon.style.transform = 'rotate(180deg)';
                } else {
                    toggleIcon.style.transform = '';
                }
            }
        });
    }

    // 初始化数量控制按钮
    const quantityMinus = newItem.querySelector('.quantity-minus');
    const quantityPlus = newItem.querySelector('.quantity-plus');
    const quantityInput = newItem.querySelector('.clothing-quantity');

    if (quantityMinus && quantityPlus && quantityInput) {
        quantityMinus.addEventListener('click', function() {
            let currentValue = parseInt(quantityInput.value) || 1;
            if (currentValue > 1) {
                quantityInput.value = currentValue - 1;
                updateItemPrice(newItem);
            }
        });

        quantityPlus.addEventListener('click', function() {
            let currentValue = parseInt(quantityInput.value) || 1;
            if (currentValue < 99) {
                quantityInput.value = currentValue + 1;
                updateItemPrice(newItem);
            }
        });

        quantityInput.addEventListener('change', function() {
            let value = parseInt(this.value) || 1;
            if (value < 1) value = 1;
            if (value > 99) value = 99;
            this.value = value;
            updateItemPrice(newItem);
        });
    }

    // 初始化删除按钮
    const removeBtn = newItem.querySelector('.remove-item');
    if (removeBtn) {
        removeBtn.addEventListener('click', function() {
            // 添加动画效果
            newItem.style.opacity = '0';
            newItem.style.transform = 'translateX(100px)';
            newItem.style.transition = 'opacity 0.3s, transform 0.3s';

            setTimeout(() => {
                if (confirm('确定要删除这件衣物吗？')) {
                    newItem.remove();
                } else {
                    // 恢复样式
                    newItem.style.opacity = '';
                    newItem.style.transform = '';
                }
            }, 300);
        });
    }

    // 初始化添加照片按钮
    const addPhotoBtn = newItem.querySelector('.add-photo-btn');
    if (addPhotoBtn) {
        addPhotoBtn.addEventListener('click', function() {
            // 设置当前操作的衣物项
            currentPhotoItem = newItem;

            // 打开摄像头
            openCamera();
        });
    }

    // 初始化衣物项滑动操作
    initializeSwipeGesture(newItem);

    // 设置初始价格（默认只有洗衣服务）
    updateItemPrice(newItem);

    return newItem;
}

/**
 * 初始化衣物项滑动操作
 * @param {Element} item 衣物项元素
 */
function initializeSwipeGesture(item) {
    let startX, startY, moveX, moveY;
    let isVScroll = false;

    item.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
        isVScroll = false;
    }, { passive: true });

    item.addEventListener('touchmove', (e) => {
        moveX = e.touches[0].clientX;
        moveY = e.touches[0].clientY;

        // 判断是否为垂直滚动
        if (!isVScroll) {
            isVScroll = Math.abs(moveY - startY) > Math.abs(moveX - startX);
        }

        // 如果不是垂直滚动，且水平滑动超过50px
        if (!isVScroll && Math.abs(moveX - startX) > 50) {
            e.preventDefault(); // 阻止默认滚动

            // 应用滑动效果
            const diffX = moveX - startX;
            if (diffX < 0) {
                // 左滑，露出删除按钮
                item.style.transform = `translateX(${diffX}px)`;
            }
        }
    });

    item.addEventListener('touchend', (e) => {
        if (!isVScroll) {
            const diffX = e.changedTouches[0].clientX - startX;

            if (diffX < -100) {
                // 左滑超过阈值，显示删除确认
                item.querySelector('.remove-item').click();
            }

            // 恢复位置
            item.style.transform = '';
        }
    });
}

/**
 * 打开摄像头
 */
function openCamera() {
    const cameraModal = document.getElementById('cameraModal');
    const cameraFeed = document.getElementById('modalCameraFeed');

    if (!cameraModal || !cameraFeed) {
        console.error('未找到摄像头模态框或视频元素');
        return;
    }

    // 显示模态框
    cameraModal.style.display = 'block';

    // 获取摄像头权限
    navigator.mediaDevices.getUserMedia({
        video: {
            facingMode: 'environment', // 优先使用后置摄像头
            width: { ideal: 1280 },
            height: { ideal: 720 }
        },
        audio: false
    })
    .then(stream => {
        cameraStream = stream;
        cameraFeed.srcObject = stream;
    })
    .catch(error => {
        console.error('获取摄像头失败:', error);
        cameraModal.style.display = 'none';
        showError('无法访问摄像头，请检查权限设置');
    });
}

/**
 * 初始化打印功能
 */
function initializePrintFunctions() {
    // 关闭打印模态框
    document.querySelectorAll('.close-print-modal').forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('#receiptModal, #labelModal');
            if (modal) {
                modal.style.display = 'none';
            }
        });
    });

    // 打印小票按钮
    const printReceiptBtn = document.getElementById('printReceiptBtn');
    if (printReceiptBtn) {
        printReceiptBtn.addEventListener('click', function() {
            printReceiptMobile();
        });
    }

    // 打印所有水洗唛按钮
    const printAllLabelsBtn = document.getElementById('printAllLabelsBtn');
    if (printAllLabelsBtn) {
        printAllLabelsBtn.addEventListener('click', function() {
            printAllLabels();
        });
    }

    // 打印选中水洗唛按钮
    const printSelectedLabelBtn = document.getElementById('printSelectedLabelBtn');
    if (printSelectedLabelBtn) {
        printSelectedLabelBtn.addEventListener('click', function() {
            const labelSelect = document.getElementById('labelSelect');
            if (labelSelect && labelSelect.value !== 'all') {
                printSelectedLabel(labelSelect.value);
            } else {
                printAllLabels();
            }
        });
    }
}

/**
 * 显示小票打印预览
 * @param {string} orderId 订单ID
 */
function showReceiptModal(orderId) {
    const modal = document.getElementById('receiptModal');
    const contentDiv = document.getElementById('receiptContent');

    if (!modal || !contentDiv) {
        console.error('未找到小票模态框或内容区域');
        return;
    }

    // 显示加载指示器
    showLoading('正在生成小票...');

    // 获取小票内容
    fetch(`/receipt/${orderId}`)
        .then(response => response.text())
        .then(html => {
            // 隐藏加载指示器
            hideLoading();

            // 显示小票内容
            contentDiv.innerHTML = html;

            // 显示模态框
            modal.style.display = 'block';
        })
        .catch(error => {
            hideLoading();
            console.error('获取小票内容失败:', error);
            showError('获取小票内容失败，请重试');
        });
}

/**
 * 显示水洗唛打印预览
 * @param {string} orderId 订单ID
 */
function showLabelModal(orderId) {
    const modal = document.getElementById('labelModal');
    const contentDiv = document.getElementById('labelContent');
    const labelSelect = document.getElementById('labelSelect');

    if (!modal || !contentDiv || !labelSelect) {
        console.error('未找到水洗唛模态框或内容区域');
        return;
    }

    // 显示加载指示器
    showLoading('正在生成水洗唛...');

    // 获取水洗唛内容
    fetch(`/labels/${orderId}`)
        .then(response => response.json())
        .then(data => {
            // 隐藏加载指示器
            hideLoading();

            if (data.labels && data.labels.length > 0) {
                // 清空选择器
                labelSelect.innerHTML = '<option value="all">打印所有水洗唛</option>';

                // 添加每个衣物的选项
                data.labels.forEach((label, index) => {
                    const option = document.createElement('option');
                    option.value = index.toString();
                    option.textContent = `${index + 1}. ${label.clothing_name} (${label.clothing_color})`;
                    labelSelect.appendChild(option);
                });

                // 显示水洗唛内容
                contentDiv.innerHTML = data.html;

                // 显示模态框
                modal.style.display = 'block';
            } else {
                showError('没有找到可打印的水洗唛');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('获取水洗唛内容失败:', error);
            showError('获取水洗唛内容失败，请重试');
        });
}

/**
 * 移动端打印小票（兼容性函数）
 * 调用统一的打印接口
 */
function printReceiptMobile() {
    const receiptContent = document.getElementById('receiptContent');

    if (!receiptContent) {
        console.error('未找到小票内容区域');
        return;
    }

    // 创建打印窗口
    const printWindow = window.open('', '_blank');

    // 添加打印样式
    printWindow.document.write(`
        <html>
        <head>
            <title>打印小票</title>
            <link rel="stylesheet" href="/static/css/print-styles.css">
            <style>
                @media print {
                    body {
                        width: 80mm;
                        margin: 0;
                        padding: 0;
                    }
                }
            </style>
        </head>
        <body>
            ${receiptContent.innerHTML}
            <script>
                window.onload = function() {
                    window.print();
                    setTimeout(function() { window.close(); }, 500);
                };
            </script>
        </body>
        </html>
    `);

    printWindow.document.close();
}

/**
 * 打印所有水洗唛
 */
function printAllLabels() {
    const labelContent = document.getElementById('labelContent');

    if (!labelContent) {
        console.error('未找到水洗唛内容区域');
        return;
    }

    // 创建打印窗口
    const printWindow = window.open('', '_blank');

    // 添加打印样式
    printWindow.document.write(`
        <html>
        <head>
            <title>打印水洗唛</title>
            <link rel="stylesheet" href="/static/css/print-styles.css">
            <style>
                @media print {
                    .label-item {
                        page-break-after: always;
                        width: 50mm;
                        height: 30mm;
                        margin: 0;
                        padding: 0;
                    }
                }
            </style>
        </head>
        <body>
            ${labelContent.innerHTML}
            <script>
                window.onload = function() {
                    window.print();
                    setTimeout(function() { window.close(); }, 500);
                };
            </script>
        </body>
        </html>
    `);

    printWindow.document.close();
}

/**
 * 打印选中的水洗唛
 * @param {string} index 水洗唛索引
 */
function printSelectedLabel(index) {
    const labelItems = document.querySelectorAll('#labelContent .label-item');

    if (!labelItems || labelItems.length === 0) {
        console.error('未找到水洗唛项');
        return;
    }

    const selectedLabel = labelItems[parseInt(index)];

    if (!selectedLabel) {
        console.error('未找到选中的水洗唛');
        return;
    }

    // 创建打印窗口
    const printWindow = window.open('', '_blank');

    // 添加打印样式
    printWindow.document.write(`
        <html>
        <head>
            <title>打印水洗唛</title>
            <link rel="stylesheet" href="/static/css/print-styles.css">
            <style>
                @media print {
                    .label-item {
                        width: 50mm;
                        height: 30mm;
                        margin: 0;
                        padding: 0;
                    }
                }
            </style>
        </head>
        <body>
            ${selectedLabel.outerHTML}
            <script>
                window.onload = function() {
                    window.print();
                    setTimeout(function() { window.close(); }, 500);
                };
            </script>
        </body>
        </html>
    `);

    printWindow.document.close();
}

/**
 * 搜索客户
 * @param {string} phone 手机号
 */
async function searchCustomerByPhone(phone) {
    if (!phone) return;

    try {
        const response = await fetch(`/search_customer?phone=${phone}`);
        const data = await response.json();

        if (data.found) {
            // 显示客户信息
            document.getElementById('customerNameDisplay').textContent = data.name;
            document.getElementById('customerBalance').textContent = data.balance.toFixed(2);
            document.getElementById('customerDetails').classList.remove('hidden');
            document.getElementById('newCustomerForm').classList.add('hidden');

            // 更新客户ID
            customerId = data.id;

            // 更新客户详情
            customerDetails = {
                name: data.name,
                phone: phone,
                balance: data.balance
            };

            // 更新摘要页面的客户名称
            document.getElementById('summaryName').textContent = data.name;

            return true;
        } else {
            // 显示新客户表单
            document.getElementById('customerDetails').classList.add('hidden');
            document.getElementById('newCustomerForm').classList.remove('hidden');

            // 重置客户ID
            customerId = null;

            return false;
        }
    } catch (error) {
        console.error('搜索客户失败:', error);
        showError('搜索客户失败，请重试');
        return false;
    }
}

/**
 * 收集订单数据
 * @returns {Object} 订单数据
 */
function collectOrderData() {
    // 获取客户类型
    const customerType = document.querySelector('.customer-type-option.active').getAttribute('data-type');

    // 初始化订单数据对象
    let orderData = {};

    if (customerType === 'normal') {
        // 普通客户
        orderData.is_mall_customer = false;
        orderData.customer_name = document.getElementById('customerNameDisplay').textContent || document.getElementById('customerName').value;
        orderData.customer_phone = document.getElementById('customerPhone').value;
        orderData.address = document.getElementById('address').value;

        // 如果有客户ID，也添加到数据中
        if (customerId) {
            orderData.customer_id = customerId;
        }
    } else {
        // 商场客户
        const mallCustomerInput = document.getElementById('mallCustomerName');
        orderData.is_mall_customer = true;
        orderData.mall_customer_id = mallCustomerInput.dataset.mallCustomerId;
        orderData.mall_customer_name = mallCustomerInput.value;
        orderData.mall_customer_phone = document.getElementById('mallCustomerPhone').value;
    }

    // 支付方式
    orderData.payment_method = document.getElementById('paymentMethod').value;

    // 总金额
    orderData.total_amount = parseFloat(document.getElementById('totalAmount').textContent);

    // 衣物信息
    const items = [];
    document.querySelectorAll('.clothing-item').forEach(item => {
        const nameSelect = item.querySelector('.clothing-name');
        const name = nameSelect.value;
        const color = item.querySelector('.clothing-color').value;
        const price = parseFloat(item.querySelector('.clothing-price').value || 0);
        const flaw = item.querySelector('.clothing-flaw').value;
        const remarks = item.querySelector('.clothing-note').value;

        // 服务类型
        const serviceTypes = [];
        item.querySelectorAll('.service-type:checked').forEach(checkbox => {
            serviceTypes.push(checkbox.value);
        });

        // 特殊服务要求
        const specialRequirements = {};

        if (serviceTypes.includes('洗衣')) {
            specialRequirements.wash = {
                requirement: '',
                price: parseFloat(item.querySelector('.wash-price').value || 0)
            };
        }

        if (serviceTypes.includes('织补')) {
            specialRequirements.darn = {
                requirement: item.querySelector('.darn-requirement').value,
                price: parseFloat(item.querySelector('.darn-price').value || 0)
            };
        }

        if (serviceTypes.includes('改衣')) {
            specialRequirements.alter = {
                requirement: item.querySelector('.alter-requirement').value,
                price: parseFloat(item.querySelector('.alter-price').value || 0)
            };
        }

        // 照片
        const photos = [];
        item.querySelectorAll('.photo-thumbnail').forEach(thumbnail => {
            photos.push(thumbnail.dataset.fullImage);
        });

        // 获取数量
        const quantityInput = item.querySelector('.clothing-quantity');
        const quantity = parseInt(quantityInput?.value || 1);

        items.push({
            name: name,
            color: color,
            price: price,
            quantity: quantity,  // 添加数量字段
            flaw: flaw,
            remarks: remarks,
            serviceTypes: serviceTypes,
            specialRequirements: specialRequirements,
            photos: photos
        });
    });

    // 添加衣物项目到订单数据
    orderData.items = items;

    console.log("收集的订单数据:", orderData);

    return orderData;
}

/**
 * 显示加载指示器
 * @param {string} message 加载消息
 */
function showLoading(message = '加载中...') {
    const processingIndicator = document.getElementById('processingIndicator');
    const processingText = document.getElementById('processingText');

    if (processingIndicator && processingText) {
        processingText.textContent = message;
        processingIndicator.style.display = 'flex';
    }
}

/**
 * 隐藏加载指示器
 */
function hideLoading() {
    const processingIndicator = document.getElementById('processingIndicator');

    if (processingIndicator) {
        processingIndicator.style.display = 'none';
    }
}

/**
 * 显示成功提示
 * @param {string} message 成功消息
 */
function showSuccess(message) {
    // 创建成功提示元素
    const successDiv = document.createElement('div');
    successDiv.style.position = 'fixed';
    successDiv.style.bottom = '80px';
    successDiv.style.left = '50%';
    successDiv.style.transform = 'translateX(-50%)';
    successDiv.style.backgroundColor = '#d4edda';
    successDiv.style.color = '#155724';
    successDiv.style.padding = '12px 20px';
    successDiv.style.borderRadius = '8px';
    successDiv.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
    successDiv.style.zIndex = '9999';
    successDiv.style.maxWidth = '90%';
    successDiv.style.textAlign = 'center';
    successDiv.style.transition = 'opacity 0.5s';
    successDiv.textContent = message;

    // 添加到页面
    document.body.appendChild(successDiv);

    // 3秒后自动消失
    setTimeout(() => {
        successDiv.style.opacity = '0';
        setTimeout(() => {
            if (document.body.contains(successDiv)) {
                document.body.removeChild(successDiv);
            }
        }, 500);
    }, 3000);
}

/**
 * 显示错误提示
 * @param {string} message 错误消息
 */
function showError(message) {
    // 创建错误提示元素
    const errorDiv = document.createElement('div');
    errorDiv.style.position = 'fixed';
    errorDiv.style.bottom = '80px';
    errorDiv.style.left = '50%';
    errorDiv.style.transform = 'translateX(-50%)';
    errorDiv.style.backgroundColor = '#f8d7da';
    errorDiv.style.color = '#721c24';
    errorDiv.style.padding = '12px 20px';
    errorDiv.style.borderRadius = '8px';
    errorDiv.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
    errorDiv.style.zIndex = '9999';
    errorDiv.style.maxWidth = '90%';
    errorDiv.style.textAlign = 'center';
    errorDiv.style.transition = 'opacity 0.5s';
    errorDiv.textContent = message;

    // 添加到页面
    document.body.appendChild(errorDiv);

    // 3秒后自动消失
    setTimeout(() => {
        errorDiv.style.opacity = '0';
        setTimeout(() => {
            if (document.body.contains(errorDiv)) {
                document.body.removeChild(errorDiv);
            }
        }, 500);
    }, 3000);
}

/**
 * 显示图片预览
 * @param {string} imageUrl 图片URL
 */
function showImagePreview(imageUrl) {
    // 创建预览容器
    const previewContainer = document.createElement('div');
    previewContainer.style.position = 'fixed';
    previewContainer.style.top = '0';
    previewContainer.style.left = '0';
    previewContainer.style.width = '100%';
    previewContainer.style.height = '100%';
    previewContainer.style.backgroundColor = 'rgba(0,0,0,0.9)';
    previewContainer.style.display = 'flex';
    previewContainer.style.justifyContent = 'center';
    previewContainer.style.alignItems = 'center';
    previewContainer.style.zIndex = '2000';

    // 创建图片元素
    const image = document.createElement('img');
    image.src = imageUrl;
    image.style.maxWidth = '90%';
    image.style.maxHeight = '90%';
    image.style.objectFit = 'contain';

    // 添加到容器
    previewContainer.appendChild(image);

    // 添加到页面
    document.body.appendChild(previewContainer);

    // 点击关闭预览
    previewContainer.addEventListener('click', function() {
        document.body.removeChild(previewContainer);
    });
}

/**
 * 修复iOS中的一些问题
 */
function fixIOSIssues() {
    // 修复iOS中的双击缩放问题
    document.addEventListener('touchend', function(event) {
        const now = Date.now();
        const lastTouch = this.lastTouch || now + 1;
        const delta = now - lastTouch;

        if (delta < 500 && delta > 0) {
            event.preventDefault();
        }

        this.lastTouch = now;
    }, false);

    // 修复iOS中的输入框聚焦问题
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            // 滚动到输入框位置
            setTimeout(() => {
                const scrollY = window.scrollY;
                window.scrollTo(0, scrollY);
            }, 100);
        });
    });
}

/**
 * 移动端折扣相关函数
 */

/**
 * 应用折扣到移动端衣物项
 * @param {Element} itemElement 衣物项元素
 */
function applyDiscountToItemMobile(itemElement) {
    if (!window.currentMallCustomer || !itemElement) {
        return;
    }

    const nameSelect = itemElement.querySelector('.clothing-name');
    const productName = nameSelect.value;

    if (!productName) {
        return;
    }

    // 获取产品类型
    const productType = getProductTypeFromNameMobile(productName);

    // 计算当前总价（基于选中的服务）
    let currentTotalPrice = 0;
    const serviceCheckboxes = itemElement.querySelectorAll('.service-type:checked');

    serviceCheckboxes.forEach(checkbox => {
        const serviceType = checkbox.value;
        if (serviceType === '洗衣') {
            const washPriceInput = itemElement.querySelector('.wash-price');
            currentTotalPrice += parseFloat(washPriceInput.value || 0);
        } else if (serviceType === '织补') {
            const darnPriceInput = itemElement.querySelector('.darn-price');
            currentTotalPrice += parseFloat(darnPriceInput.value || 0);
        } else if (serviceType === '改衣') {
            const alterPriceInput = itemElement.querySelector('.alter-price');
            currentTotalPrice += parseFloat(alterPriceInput.value || 0);
        } else if (serviceType === '其他') {
            const otherPriceInput = itemElement.querySelector('.other-price');
            currentTotalPrice += parseFloat(otherPriceInput.value || 0);
        }
    });

    // 应用折扣
    const discountResult = applyMallCustomerDiscount(productName, productType, currentTotalPrice);

    // 获取价格输入框
    const priceInput = itemElement.querySelector('.clothing-price');

    if (discountResult.discountAmount > 0) {
        // 更新价格显示
        updatePriceInputWithDiscount(priceInput, discountResult);

        // 更新价格输入框的值
        priceInput.value = discountResult.discountedPrice.toFixed(2);

        console.log(`移动端应用折扣到衣物 ${productName}: 原价¥${discountResult.originalPrice} → 折后¥${discountResult.discountedPrice.toFixed(2)}`);
    } else {
        // 没有折扣时，清除折扣信息显示
        const discountDisplay = priceInput.parentElement.querySelector('.discount-info');
        if (discountDisplay) {
            discountDisplay.remove();
        }

        // 更新价格输入框的值
        priceInput.value = currentTotalPrice.toFixed(2);
    }

    // 更新订单总计折扣显示
    updateOrderTotalDiscountDisplay();
}

/**
 * 应用折扣到所有已存在的移动端衣物项
 */
function applyDiscountsToExistingItemsMobile() {
    const clothingItems = document.querySelectorAll('.clothing-item');
    clothingItems.forEach(item => {
        const nameSelect = item.querySelector('.clothing-name');
        if (nameSelect && nameSelect.value) {
            applyDiscountToItemMobile(item);
        }
    });

    // 更新订单总计折扣显示
    updateOrderTotalDiscountDisplay();
}

/**
 * 移动端产品类型映射
 * @param {string} productName 产品名称
 * @returns {string} 产品类型
 */
function getProductTypeFromNameMobile(productName) {
    // 简单的产品类型映射，可以根据实际需要扩展
    const typeMapping = {
        '衬衫': '上衣',
        'T恤': '上衣',
        '西装外套': '外套',
        '大衣': '外套',
        '西裤': '裤装',
        '牛仔裤': '裤装',
        '裙子': '裙装',
        '连衣裙': '裙装',
        '羽绒服': '外套',
        '毛衣': '上衣',
        '针织衫': '上衣',
        '风衣': '外套',
        '夹克': '外套',
        '短裤': '裤装',
        '长裤': '裤装',
        '半身裙': '裙装',
        '连衣裙': '裙装'
    };

    return typeMapping[productName] || '其他';
}